# -*- coding: utf-8 -*-
"""
UNO牌堆类实现
管理抽牌堆、弃牌堆和洗牌逻辑
"""

import random
from typing import List, Optional
from .card import Card, CardColor, CardType


class Deck:
    """UNO牌堆管理类"""
    
    def __init__(self):
        """初始化牌组"""
        self.draw_pile: List[Card] = []
        self.discard_pile: List[Card] = []
        self.reset()
    
    def _create_standard_deck(self) -> List[Card]:
        """创建标准UNO牌组（108张牌）"""
        cards = []
        
        # 四种颜色的牌
        for color in [CardColor.RED, CardColor.BLUE, CardColor.GREEN, CardColor.YELLOW]:
            # 数字牌 0-9
            # 数字0只有1张，数字1-9每个有2张
            cards.append(Card(color, CardType.NUMBER, 0))
            for number in range(1, 10):
                cards.append(Card(color, CardType.NUMBER, number))
                cards.append(Card(color, CardType.NUMBER, number))
            
            # 功能牌每种2张
            for _ in range(2):
                cards.append(Card(color, CardType.SKIP))
                cards.append(Card(color, CardType.REVERSE))
                cards.append(Card(color, CardType.DRAW_TWO))
        
        # 万能牌（黑色）
        for _ in range(4):
            cards.append(Card(CardColor.BLACK, CardType.WILD))
            cards.append(Card(CardColor.BLACK, CardType.WILD_DRAW_FOUR))
        
        return cards
    
    def shuffle(self):
        """洗牌"""
        random.shuffle(self.draw_pile)
    
    def draw_card(self) -> Optional[Card]:
        """
        从抽牌堆抽一张牌
        
        Returns:
            Card: 抽到的牌，如果牌堆为空则返回None
        """
        if not self.draw_pile:
            # 抽牌堆为空，从弃牌堆重新洗牌
            self._reshuffle_from_discard()
        
        if self.draw_pile:
            return self.draw_pile.pop()
        return None
    
    def draw_cards(self, count: int) -> List[Card]:
        """
        抽取指定数量的牌
        
        Args:
            count: 要抽取的牌数
            
        Returns:
            List[Card]: 抽到的牌列表
        """
        cards = []
        for _ in range(count):
            card = self.draw_card()
            if card:
                cards.append(card)
            else:
                break  # 没有足够的牌
        return cards
    
    def play_card(self, card: Card):
        """
        将牌放到弃牌堆
        
        Args:
            card: 要放置的牌
        """
        self.discard_pile.append(card)
    
    def get_top_card(self) -> Optional[Card]:
        """
        获取弃牌堆顶牌（当前桌面牌）
        
        Returns:
            Card: 顶牌，如果弃牌堆为空则返回None
        """
        if self.discard_pile:
            return self.discard_pile[-1]
        return None
    
    def _reshuffle_from_discard(self):
        """从弃牌堆重新洗牌到抽牌堆"""
        if len(self.discard_pile) <= 1:
            return  # 弃牌堆没有足够的牌来重新洗牌
        
        # 保留弃牌堆顶牌
        top_card = self.discard_pile.pop()
        
        # 将其余弃牌堆的牌洗入抽牌堆
        self.draw_pile.extend(self.discard_pile)
        self.discard_pile = [top_card]
        
        self.shuffle()
    
    def deal_initial_hands(self, num_players: int, cards_per_player: int = 7) -> List[List[Card]]:
        """
        发初始手牌
        
        Args:
            num_players: 玩家数量
            cards_per_player: 每人发牌数量
            
        Returns:
            List[List[Card]]: 每个玩家的手牌
        """
        hands = []
        for _ in range(num_players):
            hand = self.draw_cards(cards_per_player)
            hands.append(hand)
        return hands
    
    def start_game(self) -> Optional[Card]:
        """
        开始游戏，翻开第一张牌
        
        Returns:
            Card: 开始牌，如果是万能牌会重新抽取
        """
        while True:
            card = self.draw_card()
            if not card:
                return None
            
            # 如果第一张牌是万能牌，重新抽取
            if card.card_type in [CardType.WILD, CardType.WILD_DRAW_FOUR]:
                # 将万能牌放回抽牌堆底部
                self.draw_pile.insert(0, card)
                self.shuffle()
                continue
            
            self.play_card(card)
            return card
    
    def get_remaining_count(self) -> int:
        """获取抽牌堆剩余牌数"""
        return len(self.draw_pile)
    
    def get_discard_count(self) -> int:
        """获取弃牌堆牌数"""
        return len(self.discard_pile)
    
    def is_empty(self) -> bool:
        """检查是否没有牌可抽"""
        return len(self.draw_pile) == 0 and len(self.discard_pile) <= 1
    
    def get_deck_info(self) -> dict:
        """获取牌堆信息（用于调试和显示）"""
        return {
            'draw_pile_count': self.get_remaining_count(),
            'discard_pile_count': self.get_discard_count(),
            'top_card': self.get_top_card().to_dict() if self.get_top_card() else None,
            'is_empty': self.is_empty()
        }
    
    def reset(self):
        """重置牌组到初始状态"""
        self.draw_pile = self._create_standard_deck()
        self.discard_pile = []
        self.shuffle()
    
    def __str__(self) -> str:
        return f"Deck(抽牌堆:{len(self.draw_pile)}张, 弃牌堆:{len(self.discard_pile)}张)" 