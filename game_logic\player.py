# -*- coding: utf-8 -*-
"""
UNO玩家类实现
包含真实玩家和AI机器人的实现
"""

import random
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from .card import Card, CardColor, CardType


class Player(ABC):
    """玩家抽象基类"""
    
    def __init__(self, player_id: int, name: str):
        """
        初始化玩家
        
        Args:
            player_id: 玩家ID
            name: 玩家名称
        """
        self.player_id = player_id
        self.name = name
        self.hand: List[Card] = []  # 手牌
        self.is_ai = False
    
    def add_card(self, card: Card):
        """添加卡牌到手牌"""
        self.hand.append(card)
    
    def remove_card(self, card: Card) -> bool:
        """从手牌移除卡牌"""
        if card in self.hand:
            self.hand.remove(card)
            return True
        return False
    
    def get_hand_size(self) -> int:
        """获取手牌数量"""
        return len(self.hand)
    
    def has_card(self, card: Card) -> bool:
        """检查是否拥有某张牌"""
        return card in self.hand
    
    def get_playable_cards(self, top_card: Card, chosen_color: Optional[CardColor] = None) -> List[Card]:
        """获取可以出的牌"""
        playable = []
        for card in self.hand:
            if card.can_play_on(top_card, chosen_color):
                playable.append(card)
        return playable
    
    @abstractmethod
    def choose_card(self, game_state: Dict[str, Any]) -> Optional[Card]:
        """
        选择要出的牌
        
        Args:
            game_state: 游戏状态信息
            
        Returns:
            Card: 选择的牌，None表示无法出牌
        """
        pass
    
    @abstractmethod
    def choose_color(self, game_state: Dict[str, Any]) -> CardColor:
        """
        选择万能牌的颜色
        
        Args:
            game_state: 游戏状态信息
            
        Returns:
            CardColor: 选择的颜色
        """
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'player_id': self.player_id,
            'name': self.name,
            'hand_size': len(self.hand),
            'is_ai': self.is_ai
        }


class HumanPlayer(Player):
    """真实玩家类"""
    
    def __init__(self, player_id: int, name: str = "玩家"):
        super().__init__(player_id, name)
        self.is_ai = False
    
    def choose_card(self, game_state: Dict[str, Any]) -> Optional[Card]:
        """真实玩家通过界面选择卡牌，这里返回None等待前端交互"""
        # 真实玩家的选择由前端界面处理，这里不自动选择
        return None
    
    def choose_color(self, game_state: Dict[str, Any]) -> CardColor:
        """真实玩家通过界面选择颜色，默认返回红色"""
        # 真实玩家的颜色选择由前端界面处理
        return CardColor.RED


class AIPlayer(Player):
    """AI机器人玩家类"""
    
    def __init__(self, player_id: int, name: str = None, difficulty: str = "medium"):
        """
        初始化AI玩家
        
        Args:
            player_id: 玩家ID
            name: 玩家名称
            difficulty: 难度等级 (easy, medium, hard)
        """
        if name is None:
            name = f"机器人{player_id}"
        super().__init__(player_id, name)
        self.is_ai = True
        self.difficulty = difficulty
    
    def choose_card(self, game_state: Dict[str, Any]) -> Optional[Card]:
        """
        AI选择卡牌策略
        
        Args:
            game_state: 游戏状态信息
            
        Returns:
            Card: 选择的牌
        """
        top_card_info = game_state.get('top_card')
        if not top_card_info:
            return None
        
        # 重建顶牌对象
        top_card = Card.from_dict(top_card_info)
        chosen_color = None
        if game_state.get('chosen_color'):
            chosen_color = CardColor(game_state['chosen_color'])
        
        # 获取可出的牌
        playable_cards = self.get_playable_cards(top_card, chosen_color)
        
        if not playable_cards:
            return None
        
        # 根据难度选择策略
        if self.difficulty == "easy":
            return self._easy_strategy(playable_cards, game_state)
        elif self.difficulty == "hard":
            return self._hard_strategy(playable_cards, game_state)
        else:  # medium
            return self._medium_strategy(playable_cards, game_state)
    
    def _easy_strategy(self, playable_cards: List[Card], game_state: Dict[str, Any]) -> Card:
        """简单策略：随机选择"""
        return random.choice(playable_cards)
    
    def _medium_strategy(self, playable_cards: List[Card], game_state: Dict[str, Any]) -> Card:
        """中等策略：优先出功能牌和大点数牌"""
        # 1. 优先出功能牌（跳过、反转、+2）
        action_cards = [card for card in playable_cards if card.is_action_card() 
                       and card.card_type != CardType.WILD and card.card_type != CardType.WILD_DRAW_FOUR]
        if action_cards:
            return random.choice(action_cards)
        
        # 2. 优先出大点数的数字牌
        number_cards = [card for card in playable_cards if card.card_type == CardType.NUMBER]
        if number_cards:
            number_cards.sort(key=lambda x: x.value, reverse=True)
            return number_cards[0]
        
        # 3. 最后考虑万能牌
        return random.choice(playable_cards)
    
    def _hard_strategy(self, playable_cards: List[Card], game_state: Dict[str, Any]) -> Card:
        """困难策略：智能分析"""
        hand_sizes = game_state.get('hand_sizes', [])
        current_player = game_state.get('current_player', 0)
        direction = game_state.get('direction', 'clockwise')
        
        # 1. 如果下家只剩1-2张牌，优先使用攻击性卡牌
        next_player_index = self._get_next_player_index(current_player, direction, len(hand_sizes))
        if next_player_index < len(hand_sizes) and hand_sizes[next_player_index] <= 2:
            # 优先使用+2、+4、跳过牌
            attack_cards = [card for card in playable_cards 
                          if card.card_type in [CardType.DRAW_TWO, CardType.WILD_DRAW_FOUR, CardType.SKIP]]
            if attack_cards:
                return random.choice(attack_cards)
        
        # 2. 分析手牌颜色分布，选择最少的颜色
        color_counts = self._analyze_color_distribution()
        
        # 3. 如果手牌很少，保留万能牌
        if len(self.hand) <= 3:
            non_wild_cards = [card for card in playable_cards 
                            if card.card_type not in [CardType.WILD, CardType.WILD_DRAW_FOUR]]
            if non_wild_cards:
                # 选择点数最高的非万能牌
                non_wild_cards.sort(key=lambda x: x.get_points(), reverse=True)
                return non_wild_cards[0]
        
        # 4. 选择能减少手牌颜色种类的牌
        best_card = self._choose_card_by_color_strategy(playable_cards, color_counts)
        if best_card:
            return best_card
        
        # 5. 默认策略
        return self._medium_strategy(playable_cards, game_state)
    
    def _get_next_player_index(self, current: int, direction: str, total_players: int) -> int:
        """获取下一个玩家索引"""
        if direction == 'clockwise':
            return (current + 1) % total_players
        else:
            return (current - 1) % total_players
    
    def _analyze_color_distribution(self) -> Dict[CardColor, int]:
        """分析手牌颜色分布"""
        color_counts = {color: 0 for color in [CardColor.RED, CardColor.BLUE, CardColor.GREEN, CardColor.YELLOW]}
        
        for card in self.hand:
            if card.color in color_counts:
                color_counts[card.color] += 1
        
        return color_counts
    
    def _choose_card_by_color_strategy(self, playable_cards: List[Card], color_counts: Dict[CardColor, int]) -> Optional[Card]:
        """根据颜色策略选择卡牌"""
        # 优先出数量最少颜色的牌
        min_count = min(color_counts.values())
        rare_colors = [color for color, count in color_counts.items() if count == min_count and count > 0]
        
        if rare_colors:
            rare_color_cards = [card for card in playable_cards if card.color in rare_colors]
            if rare_color_cards:
                # 在稀有颜色中选择点数最高的
                rare_color_cards.sort(key=lambda x: x.get_points(), reverse=True)
                return rare_color_cards[0]
        
        return None
    
    def choose_color(self, game_state: Dict[str, Any]) -> CardColor:
        """
        AI选择万能牌颜色策略
        
        Args:
            game_state: 游戏状态信息
            
        Returns:
            CardColor: 选择的颜色
        """
        # 分析手牌中各颜色的数量
        color_counts = self._analyze_color_distribution()
        
        # 选择手牌中数量最多的颜色
        max_count = max(color_counts.values())
        if max_count > 0:
            best_colors = [color for color, count in color_counts.items() if count == max_count]
            return random.choice(best_colors)
        
        # 如果没有彩色牌，随机选择
        return random.choice([CardColor.RED, CardColor.BLUE, CardColor.GREEN, CardColor.YELLOW])
    
    def should_declare_uno(self, game_state: Dict[str, Any]) -> bool:
        """判断是否应该喊UNO"""
        # AI总是在剩余1张牌时喊UNO
        return len(self.hand) == 1
    
    def get_strategy_info(self) -> str:
        """获取策略信息"""
        strategies = {
            "easy": "随机出牌",
            "medium": "优先功能牌",
            "hard": "智能分析"
        }
        return strategies.get(self.difficulty, "未知策略")


def create_players(num_players: int = 4) -> List[Player]:
    """
    创建玩家列表（1个真实玩家 + 3个AI）
    
    Args:
        num_players: 总玩家数
        
    Returns:
        List[Player]: 玩家列表
    """
    players = []
    
    # 创建真实玩家（索引0）
    players.append(HumanPlayer(0, "您"))
    
    # 创建AI玩家
    ai_names = ["小智", "小慧", "小强"]
    ai_difficulties = ["medium", "hard", "easy"]
    
    for i in range(1, num_players):
        ai_index = i - 1
        if ai_index < len(ai_names):
            name = ai_names[ai_index]
            difficulty = ai_difficulties[ai_index % len(ai_difficulties)]
        else:
            name = f"机器人{i}"
            difficulty = "medium"
        
        players.append(AIPlayer(i, name, difficulty))
    
    return players 