# -*- coding: utf-8 -*-
"""
UNO游戏管理器
整合玩家、游戏逻辑和AI决策
"""

import time
import asyncio
from typing import List, Optional, Dict, Any
from .card import Card, CardColor, CardType
from .game import UnoGame
from .player import Player, create_players, AIPlayer


class UnoGameManager:
    """UNO游戏管理器类"""
    
    def __init__(self):
        """
        初始化游戏管理器
        """
        self.game = None
        self.players: List[Player] = []
        self.game_history: List[Dict[str, Any]] = []
        self.ai_thinking_delay = 1.0  # AI思考延迟（秒）
        self.human_player_count = 0
        self.ai_player_count = 0
    
    def add_human_player(self, name: str) -> str:
        """添加人类玩家"""
        from .player import HumanPlayer
        player_id = str(len(self.players))
        player = HumanPlayer(player_id, name)
        self.players.append(player)
        self.human_player_count += 1
        return player_id
    
    def add_ai_player(self, name: str, difficulty: str = "medium") -> str:
        """添加AI玩家"""
        from .player import AIPlayer
        player_id = str(len(self.players))
        player = AIPlayer(player_id, name, difficulty)
        self.players.append(player)
        self.ai_player_count += 1
        return player_id
    
    def start_game(self) -> Dict[str, Any]:
        """开始游戏"""
        try:
            # 创建游戏实例
            self.game = UnoGame(len(self.players))
            
            # 开始游戏
            self.game.start_game()
            
            # 给玩家发牌
            for i, player in enumerate(self.players):
                player.hand = self.game.player_hands[i]
            
            # 记录游戏开始
            self.game_history.clear()
            self._add_to_history("游戏开始", {"players": [p.to_dict() for p in self.players]})
            
            return {
                'success': True,
                'message': '游戏开始！',
                'game_state': self._get_complete_game_state(),
                'players': [p.to_dict() for p in self.players]
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'游戏启动失败：{str(e)}'
            }
    
    def play_card(self, player_id: str, card_index: int, chosen_color: Optional[CardColor] = None) -> Dict[str, Any]:
        """玩家出牌"""
        try:
            player_id_int = int(player_id)
            
            # 获取玩家手牌中的卡牌
            if player_id_int >= len(self.players) or card_index >= len(self.game.player_hands[player_id_int]):
                return {'success': False, 'message': '无效的卡牌索引'}
            
            card = self.game.player_hands[player_id_int][card_index]
            
            # 执行出牌
            result = self.game.play_card(player_id_int, card, chosen_color)
            
            if result['success']:
                # 更新玩家手牌
                self.players[player_id_int].hand = self.game.player_hands[player_id_int]
                
                # 记录历史
                self._add_to_history("玩家出牌", {
                    'player': self.players[player_id_int].name,
                    'card': str(card),
                    'chosen_color': chosen_color.value if chosen_color else None
                })
                
                # 添加完整游戏状态
                result['game_state'] = self._get_complete_game_state()
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'message': f'出牌失败：{str(e)}'
            }
    
    def draw_card(self, player_id: str) -> Dict[str, Any]:
        """玩家抽牌"""
        try:
            player_id_int = int(player_id)
            result = self.game.draw_card(player_id_int)
            
            if result['success']:
                # 更新玩家手牌
                self.players[player_id_int].hand = self.game.player_hands[player_id_int]
                
                # 记录历史
                self._add_to_history("玩家抽牌", {
                    'player': self.players[player_id_int].name,
                    'cards_drawn': result.get('cards_drawn', 1)
                })
                
                # 添加完整游戏状态
                result['game_state'] = self._get_complete_game_state()
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'message': f'抽牌失败：{str(e)}'
            }
    
    def call_uno(self, player_id: str) -> Dict[str, Any]:
        """玩家喊UNO"""
        try:
            player_id_int = int(player_id)
            success = self.game.declare_uno(player_id_int)
            
            if success:
                self._add_to_history("喊UNO", {
                    'player': self.players[player_id_int].name
                })
                
                return {
                    'success': True,
                    'message': f'{self.players[player_id_int].name} 喊了UNO！',
                    'game_state': self._get_complete_game_state()
                }
            else:
                return {
                    'success': False,
                    'message': '现在不能喊UNO'
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'喊UNO失败：{str(e)}'
            }

    async def process_ai_turn(self) -> Optional[Dict[str, Any]]:
        """处理AI回合"""
        if not self.game:
            return None
            
        current_player_id = self.game.current_player
        
        if (current_player_id >= len(self.players) or 
            not isinstance(self.players[current_player_id], AIPlayer)):
            return None
        
        ai_player = self.players[current_player_id]
        
        try:
            # AI思考延迟
            await asyncio.sleep(self.ai_thinking_delay)
            
            # 检查是否需要喊UNO
            if len(ai_player.hand) == 1 and not self.game.uno_declared[current_player_id]:
                uno_result = self.call_uno(str(current_player_id))
                if uno_result['success']:
                    return {
                        'success': True,
                        'message': f'{ai_player.name} 喊了UNO！',
                        'action': 'call_uno'
                    }
            
            # AI选择出牌
            playable_cards = []
            for i, card in enumerate(ai_player.hand):
                if self.game.can_play_card(current_player_id, card):
                    playable_cards.append((i, card))
            
            if playable_cards:
                # 简单AI策略：优先出特殊牌
                card_index, chosen_card = playable_cards[0]
                for i, card in playable_cards:
                    if card.card_type != CardType.NUMBER:
                        card_index, chosen_card = i, card
                        break
                
                # 如果是万能牌，选择颜色
                chosen_color = None
                if chosen_card.card_type in [CardType.WILD, CardType.WILD_DRAW_FOUR]:
                    # 简单策略：选择手牌中最多的颜色
                    color_count = {}
                    for card in ai_player.hand:
                        if card.color != CardColor.BLACK:
                            color_count[card.color] = color_count.get(card.color, 0) + 1
                    
                    if color_count:
                        chosen_color = max(color_count.items(), key=lambda x: x[1])[0]
                    else:
                        chosen_color = CardColor.RED  # 默认红色
                
                # AI出牌
                result = self.play_card(str(current_player_id), card_index, chosen_color)
                
                if result['success']:
                    action_msg = f'{ai_player.name} 出了 {chosen_card.get_chinese_name()}'
                    if chosen_color:
                        chinese_color_map = {
                            CardColor.RED: "红色",
                            CardColor.BLUE: "蓝色", 
                            CardColor.GREEN: "绿色",
                            CardColor.YELLOW: "黄色"
                        }
                        color_name = chinese_color_map.get(chosen_color, chosen_color.value)
                        action_msg += f' 并选择了{color_name}'
                    
                    result['message'] = action_msg
                    result['action'] = 'play_card'
                    
                    self._add_to_history("AI出牌", {
                        'player': ai_player.name,
                        'card': chosen_card.get_chinese_name(),
                        'chosen_color': chosen_color.value if chosen_color else None
                    })
                
                return result
            else:
                # AI无法出牌，抽牌
                result = self.draw_card(str(current_player_id))
                
                if result['success']:
                    result['message'] = f'{ai_player.name} 抽了牌'
                    result['action'] = 'draw_card'
                    
                    self._add_to_history("AI抽牌", {
                        'player': ai_player.name
                    })
                
                return result
                
        except Exception as e:
            return {
                'success': False,
                'message': f'AI行动失败：{str(e)}'
            }
    
    def _get_complete_game_state(self) -> Dict[str, Any]:
        """获取完整游戏状态"""
        game_state = self.game.get_game_state()
        
        # 添加玩家信息
        game_state['players'] = []
        for i, player in enumerate(self.players):
            player_info = player.to_dict()
            player_info['strategy'] = getattr(player, 'get_strategy_info', lambda: '真实玩家')()
            game_state['players'].append(player_info)
        
        # 添加历史记录
        game_state['history'] = self.game_history[-10:]  # 最近10条记录
        
        return game_state
    
    def get_player_hand(self, player_id: int) -> List[Dict[str, Any]]:
        """获取玩家手牌"""
        return self.game.get_player_hand(player_id)
    
    def get_playable_cards(self, player_id: int) -> List[Dict[str, Any]]:
        """获取玩家可出的牌"""
        return self.game.get_playable_cards(player_id)
    
    def is_current_player(self, player_id: int) -> bool:
        """检查是否为当前玩家"""
        return self.game.current_player == player_id
    
    def is_ai_player(self, player_id: int) -> bool:
        """检查是否为AI玩家"""
        if 0 <= player_id < len(self.players):
            return isinstance(self.players[player_id], AIPlayer)
        return False
    
    def _add_to_history(self, action: str, details: Dict[str, Any]):
        """添加历史记录"""
        self.game_history.append({
            'timestamp': time.time(),
            'action': action,
            'details': details
        })
        
        # 保持历史记录数量不超过100条
        if len(self.game_history) > 100:
            self.game_history = self.game_history[-100:]
    
    def reset_game(self):
        """重置游戏"""
        self.game.reset_game()
        for player in self.players:
            player.hand.clear()
        self.game_history.clear()
    
    def get_game_summary(self) -> Dict[str, Any]:
        """获取游戏摘要"""
        return {
            'total_rounds': self.game.game_round,
            'players': [p.to_dict() for p in self.players],
            'scores': self.game.scores,
            'history_count': len(self.game_history)
        }
    
    # skip_turn method removed - not part of standard UNO rules
    # In standard UNO, players must draw if they cannot play - they cannot skip voluntarily 