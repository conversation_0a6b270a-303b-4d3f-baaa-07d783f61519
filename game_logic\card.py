# -*- coding: utf-8 -*-
"""
UNO卡牌类实现
定义卡牌类型、颜色和基本行为
"""

from enum import Enum
from typing import Optional


class CardColor(Enum):
    """卡牌颜色枚举"""
    RED = "red"
    BLUE = "blue"
    GREEN = "green"
    YELLOW = "yellow"
    BLACK = "black"  # 万能牌颜色


class CardType(Enum):
    """卡牌类型枚举"""
    # 数字牌 0-9
    NUMBER = "number"
    
    # 功能牌
    SKIP = "skip"           # 跳过牌
    REVERSE = "reverse"     # 反转牌
    DRAW_TWO = "draw_two"   # +2牌
    
    # 万能牌
    WILD = "wild"               # 万能牌（变色）
    WILD_DRAW_FOUR = "wild_draw_four"  # +4万能牌


class Card:
    """UNO卡牌类"""
    
    def __init__(self, color: CardColor, card_type: CardType, number: Optional[int] = None):
        """
        初始化卡牌
        
        Args:
            color: 卡牌颜色
            card_type: 卡牌类型
            number: 数字(仅数字牌需要)
        """
        self.color = color
        self.card_type = card_type
        self.number = number
        
        # 验证卡牌的有效性
        if not self.is_valid():
            raise ValueError(f"无效的卡牌: {color}, {card_type}, {number}")
    
    def is_valid(self) -> bool:
        """检查卡牌是否有效"""
        if self.card_type == CardType.NUMBER:
            return self.number is not None and 0 <= self.number <= 9
        elif self.card_type in [CardType.WILD, CardType.WILD_DRAW_FOUR]:
            return self.color == CardColor.BLACK
        else:  # SKIP, REVERSE, DRAW_TWO
            return self.color != CardColor.BLACK and self.number is None
    
    def get_chinese_name(self) -> str:
        """获取卡牌的中文名称"""
        # 颜色映射
        color_map = {
            CardColor.RED: "红",
            CardColor.BLUE: "蓝", 
            CardColor.GREEN: "绿",
            CardColor.YELLOW: "黄",
            CardColor.BLACK: "黑"
        }
        
        # 类型映射
        if self.card_type == CardType.NUMBER:
            return f"{color_map[self.color]}{self.number}"
        elif self.card_type == CardType.SKIP:
            return f"{color_map[self.color]}禁止"
        elif self.card_type == CardType.REVERSE:
            return f"{color_map[self.color]}反转"
        elif self.card_type == CardType.DRAW_TWO:
            return f"{color_map[self.color]}罚2"
        elif self.card_type == CardType.WILD:
            return "万能"
        elif self.card_type == CardType.WILD_DRAW_FOUR:
            return "万能罚4"
        else:
            return "未知"
    
    def get_display_name(self) -> str:
        """获取显示名称（中文）"""
        return self.get_chinese_name()
    
    def can_play_on(self, other_card: 'Card', current_color: Optional[CardColor] = None) -> bool:
        """
        检查是否可以在另一张卡牌上出牌
        
        Args:
            other_card: 目标卡牌（通常是顶牌）
            current_color: 当前指定的颜色（万能牌指定的颜色）
            
        Returns:
            bool: 是否可以出牌
        """
        # 万能牌可以出在任何牌上
        if self.card_type in [CardType.WILD, CardType.WILD_DRAW_FOUR]:
            return True
        
        # 检查颜色匹配
        target_color = current_color if current_color else other_card.color
        if self.color == target_color:
            return True
        
        # 检查类型匹配（相同数字或相同功能）
        if self.card_type == other_card.card_type:
            if self.card_type == CardType.NUMBER:
                return self.number == other_card.number
            else:
                return True  # 相同的功能卡
        
        return False
    
    def get_points(self) -> int:
        """获取卡牌分数（用于计分）"""
        if self.card_type == CardType.NUMBER:
            return self.number
        elif self.card_type in [CardType.SKIP, CardType.REVERSE, CardType.DRAW_TWO]:
            return 20
        elif self.card_type in [CardType.WILD, CardType.WILD_DRAW_FOUR]:
            return 50
        return 0
    
    def is_action_card(self) -> bool:
        """判断是否为功能牌（会触发特殊效果）"""
        return self.card_type in [
            CardType.SKIP, CardType.REVERSE, CardType.DRAW_TWO, 
            CardType.WILD, CardType.WILD_DRAW_FOUR
        ]
    
    def __str__(self) -> str:
        """字符串表示（中文）"""
        return self.get_chinese_name()
    
    def __repr__(self) -> str:
        """调试用字符串表示"""
        return f"Card({self.color}, {self.card_type}, {self.number})"
    
    def __eq__(self, other) -> bool:
        """相等比较"""
        if not isinstance(other, Card):
            return False
        return (self.color == other.color and 
                self.card_type == other.card_type and 
                self.number == other.number)
    
    def __hash__(self) -> bool:
        """哈希值（用于集合操作）"""
        return hash((self.color, self.card_type, self.number))
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'color': self.color.value,
            'type': self.card_type.value,
            'number': self.number,
            'display': self.get_chinese_name(),
            'points': self.get_points(),
            'is_action': self.is_action_card()
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Card':
        """从字典创建卡牌"""
        color = CardColor(data['color'])
        card_type = CardType(data['type'])
        number = data.get('number')
        return cls(color, card_type, number) 