# -*- coding: utf-8 -*-
"""
UNO游戏主类实现
管理游戏状态、规则和流程
"""

from enum import Enum
from typing import List, Optional, Dict, Any
from .card import Card, CardColor, CardType
from .deck import Deck


class GameDirection(Enum):
    """游戏进行方向"""
    CLOCKWISE = "clockwise"      # 顺时针
    COUNTERCLOCKWISE = "counterclockwise"  # 逆时针


class GameState(Enum):
    """游戏状态"""
    WAITING = "waiting"      # 等待开始
    PLAYING = "playing"      # 游戏进行中
    FINISHED = "finished"    # 游戏结束


class UnoGame:
    """UNO游戏主类"""
    
    def __init__(self, num_players: int = 4):
        """
        初始化游戏
        
        Args:
            num_players: 玩家数量（包括真实玩家和AI）
        """
        if num_players < 2 or num_players > 4:
            raise ValueError("玩家数量必须在2-4之间")
        
        self.num_players = num_players
        self.deck = Deck()
        self.player_hands: List[List[Card]] = []  # 每个玩家的手牌
        self.current_player = 0  # 当前玩家索引
        self.direction = GameDirection.CLOCKWISE  # 游戏方向
        self.state = GameState.WAITING
        self.chosen_color: Optional[CardColor] = None  # 万能牌指定的颜色
        self.winner: Optional[int] = None  # 获胜玩家索引
        self.scores: List[int] = [0] * num_players  # 玩家分数
        
        # 游戏状态标记
        self.skip_next_player = False  # 下一位玩家是否被跳过
        self.draw_penalty = 0  # 累积的抽牌惩罚（+2和+4的累积）
        self.must_draw = False  # 当前玩家是否必须抽牌
        self.game_round = 1  # 游戏轮次
        
        # UNO相关
        self.uno_declared: List[bool] = [False] * num_players  # 谁喊了UNO
        self.uno_penalty_players: List[int] = []  # 需要UNO惩罚的玩家
    
    def start_game(self):
        """开始游戏"""
        if self.state != GameState.WAITING:
            raise RuntimeError("游戏已经开始或已结束")
        
        # 重置牌堆和状态
        self.deck.reset()
        self.player_hands = self.deck.deal_initial_hands(self.num_players, 7)
        self.current_player = 0
        self.direction = GameDirection.CLOCKWISE
        self.chosen_color = None
        self.winner = None
        self.skip_next_player = False
        self.draw_penalty = 0
        self.must_draw = False
        self.uno_declared = [False] * self.num_players
        self.uno_penalty_players = []
        
        # 翻开第一张牌
        start_card = self.deck.start_game()
        if not start_card:
            raise RuntimeError("无法翻开第一张牌")
        
        # 处理第一张牌的特殊效果
        self._handle_start_card_effect(start_card)
        
        self.state = GameState.PLAYING
    
    def _handle_start_card_effect(self, card: Card):
        """处理开始牌的特殊效果"""
        if card.card_type == CardType.SKIP:
            self.skip_next_player = True
        elif card.card_type == CardType.REVERSE:
            if self.num_players == 2:
                # 两人游戏中，反转牌等于跳过牌
                self.skip_next_player = True
            else:
                self._reverse_direction()
        elif card.card_type == CardType.DRAW_TWO:
            self.draw_penalty = 2
            self.must_draw = True
    
    def can_play_card(self, player_index: int, card: Card) -> bool:
        """
        检查玩家是否可以出这张牌
        
        Args:
            player_index: 玩家索引
            card: 要出的牌
            
        Returns:
            bool: 是否可以出牌
        """
        if self.state != GameState.PLAYING:
            return False
        
        if player_index != self.current_player:
            return False
        
        if card not in self.player_hands[player_index]:
            return False
        
        # 如果必须抽牌，不能出牌（除非出+2或+4来传递惩罚）
        if self.must_draw and self.draw_penalty > 0:
            if card.card_type not in [CardType.DRAW_TWO, CardType.WILD_DRAW_FOUR]:
                return False
        
        top_card = self.deck.get_top_card()
        if not top_card:
            return False
        
        return card.can_play_on(top_card, self.chosen_color)
    
    def play_card(self, player_index: int, card: Card, chosen_color: Optional[CardColor] = None) -> Dict[str, Any]:
        """
        玩家出牌
        
        Args:
            player_index: 玩家索引
            card: 要出的牌
            chosen_color: 如果是万能牌，指定的颜色
            
        Returns:
            Dict: 出牌结果信息
        """
        if not self.can_play_card(player_index, card):
            return {'success': False, 'message': '无法出这张牌'}
        
        # 万能牌必须指定颜色
        if card.card_type in [CardType.WILD, CardType.WILD_DRAW_FOUR]:
            if not chosen_color or chosen_color == CardColor.BLACK:
                return {'success': False, 'message': '万能牌必须指定颜色'}
        
        # 从玩家手牌中移除
        self.player_hands[player_index].remove(card)
        
        # 放到弃牌堆
        self.deck.play_card(card)
        
        # 处理万能牌的颜色选择
        if card.card_type in [CardType.WILD, CardType.WILD_DRAW_FOUR]:
            self.chosen_color = chosen_color
        else:
            self.chosen_color = None
        
        # 重置UNO声明
        self.uno_declared[player_index] = False
        
        # 检查是否获胜
        if len(self.player_hands[player_index]) == 0:
            self._end_game(player_index)
            return {
                'success': True,
                'game_ended': True,
                'winner': player_index,
                'card_played': card.to_dict(),
                'chosen_color': chosen_color.value if chosen_color else None
            }
        
        # 处理特殊卡牌效果
        card_effects = self._handle_card_effects(card)
        
        # 检查UNO（剩一张牌时必须喊UNO）
        uno_warning = self._check_uno_requirement(player_index)
        
        # 移动到下一个玩家
        self._next_turn()
        
        return {
            'success': True,
            'game_ended': False,
            'card_played': card.to_dict(),
            'chosen_color': chosen_color.value if chosen_color else None,
            'effects': card_effects,
            'uno_warning': uno_warning,
            'current_player': self.current_player
        }
    
    def _handle_card_effects(self, card: Card) -> Dict[str, Any]:
        """处理卡牌特殊效果"""
        effects = {}
        
        if card.card_type == CardType.SKIP:
            self.skip_next_player = True
            effects['skip'] = True
            
        elif card.card_type == CardType.REVERSE:
            if self.num_players == 2:
                self.skip_next_player = True
                effects['skip'] = True
            else:
                self._reverse_direction()
                effects['reverse'] = True
                
        elif card.card_type == CardType.DRAW_TWO:
            self.draw_penalty += 2
            self.must_draw = True
            effects['draw_penalty'] = 2
            
        elif card.card_type == CardType.WILD_DRAW_FOUR:
            self.draw_penalty += 4
            self.must_draw = True
            effects['draw_penalty'] = 4
            
        return effects
    
    def _check_uno_requirement(self, player_index: int) -> Optional[str]:
        """检查UNO要求"""
        if len(self.player_hands[player_index]) == 1:
            if not self.uno_declared[player_index]:
                # 应该喊UNO但没喊，记录惩罚
                self.uno_penalty_players.append(player_index)
                return f"玩家{player_index}剩余1张牌但未喊UNO，将受到惩罚"
        return None
    
    def declare_uno(self, player_index: int) -> bool:
        """
        玩家喊UNO
        
        Args:
            player_index: 玩家索引
            
        Returns:
            bool: 是否成功喊UNO
        """
        if len(self.player_hands[player_index]) == 1:
            self.uno_declared[player_index] = True
            return True
        return False
    
    def draw_card(self, player_index: int) -> Dict[str, Any]:
        """
        玩家抽牌
        
        Args:
            player_index: 玩家索引
            
        Returns:
            Dict: 抽牌结果
        """
        if self.state != GameState.PLAYING:
            return {'success': False, 'message': '游戏未进行'}
        
        if player_index != self.current_player:
            return {'success': False, 'message': '不是您的回合'}
        
        cards_to_draw = max(1, self.draw_penalty) if self.must_draw else 1
        drawn_cards = self.deck.draw_cards(cards_to_draw)
        
        if not drawn_cards:
            return {'success': False, 'message': '牌堆已空'}
        
        # 添加到玩家手牌
        self.player_hands[player_index].extend(drawn_cards)
        
        # 重置抽牌惩罚状态
        if self.must_draw:
            self.must_draw = False
            self.draw_penalty = 0
            # 抽牌后自动跳过回合
            self._next_turn()
        
        return {
            'success': True,
            'cards_drawn': len(drawn_cards),
            'drawn_cards': [card.to_dict() for card in drawn_cards],
            'hand_size': len(self.player_hands[player_index]),
            'turn_ended': self.must_draw  # 如果是惩罚性抽牌，回合结束
        }
    
    def _reverse_direction(self):
        """反转游戏方向"""
        if self.direction == GameDirection.CLOCKWISE:
            self.direction = GameDirection.COUNTERCLOCKWISE
        else:
            self.direction = GameDirection.CLOCKWISE
    
    def _next_turn(self):
        """移动到下一个玩家"""
        if self.skip_next_player:
            # 跳过下一个玩家
            self.skip_next_player = False
            if self.direction == GameDirection.CLOCKWISE:
                self.current_player = (self.current_player + 2) % self.num_players
            else:
                self.current_player = (self.current_player - 2) % self.num_players
        else:
            # 正常移动
            if self.direction == GameDirection.CLOCKWISE:
                self.current_player = (self.current_player + 1) % self.num_players
            else:
                self.current_player = (self.current_player - 1) % self.num_players
        
        # 处理UNO惩罚
        self._process_uno_penalties()
    
    def _process_uno_penalties(self):
        """处理UNO惩罚"""
        for player_index in self.uno_penalty_players:
            # UNO惩罚：抽2张牌
            penalty_cards = self.deck.draw_cards(2)
            self.player_hands[player_index].extend(penalty_cards)
        
        self.uno_penalty_players.clear()
    
    def _end_game(self, winner_index: int):
        """结束游戏"""
        self.state = GameState.FINISHED
        self.winner = winner_index
        
        # 计算分数
        self._calculate_scores()
    
    def _calculate_scores(self):
        """计算游戏分数"""
        winner_score = 0
        
        # 计算其他玩家手牌总分，加给获胜者
        for i, hand in enumerate(self.player_hands):
            if i != self.winner:
                for card in hand:
                    winner_score += card.get_points()
        
        self.scores[self.winner] += winner_score
    
    def get_game_state(self) -> Dict[str, Any]:
        """获取游戏状态"""
        return {
            'state': self.state.value,
            'current_player': self.current_player,
            'direction': self.direction.value,
            'chosen_color': self.chosen_color.value if self.chosen_color else None,
            'winner': self.winner,
            'scores': self.scores,
            'hand_sizes': [len(hand) for hand in self.player_hands],
            'top_card': self.deck.get_top_card().to_dict() if self.deck.get_top_card() else None,
            'deck_info': self.deck.get_deck_info(),
            'must_draw': self.must_draw,
            'draw_penalty': self.draw_penalty,
            'uno_declared': self.uno_declared,
            'game_round': self.game_round
        }
    
    def get_player_hand(self, player_index: int) -> List[Dict[str, Any]]:
        """获取指定玩家的手牌"""
        if 0 <= player_index < self.num_players:
            return [card.to_dict() for card in self.player_hands[player_index]]
        return []
    
    def get_playable_cards(self, player_index: int) -> List[Dict[str, Any]]:
        """获取玩家可以出的牌"""
        if player_index != self.current_player or self.state != GameState.PLAYING:
            return []
        
        playable = []
        for card in self.player_hands[player_index]:
            if self.can_play_card(player_index, card):
                playable.append(card.to_dict())
        
        return playable
    
    def reset_game(self):
        """重置游戏到初始状态"""
        self.deck.reset()
        self.player_hands = []
        self.current_player = 0
        self.direction = GameDirection.CLOCKWISE
        self.state = GameState.WAITING
        self.chosen_color = None
        self.winner = None
        self.skip_next_player = False
        self.draw_penalty = 0
        self.must_draw = False
        self.uno_declared = [False] * self.num_players
        self.uno_penalty_players = []
        self.game_round += 1 