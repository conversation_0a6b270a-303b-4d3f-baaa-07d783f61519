/**
 * UNO游戏前端JavaScript逻辑
 * 处理WebSocket通信、游戏状态管理、UI交互
 */

class UnoGameClient {
    constructor() {
        // WebSocket连接
        this.socket = null;
        this.connected = false;
        
        // 游戏状态
        this.gameState = null;
        this.playerInfo = null;
        this.selectedCard = null;
        this.waitingForColorChoice = false;
        
        // UI元素缓存
        this.elements = {};
        
        // 初始化
        this.initializeElements();
        this.setupEventListeners();
        this.connectToServer();
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        // 屏幕元素
        this.elements.loadingScreen = document.getElementById('loading-screen');
        this.elements.startScreen = document.getElementById('start-screen');
        this.elements.gameScreen = document.getElementById('game-screen');
        
        // 开始界面
        this.elements.playerNameInput = document.getElementById('player-name');
        this.elements.startGameBtn = document.getElementById('start-game-btn');
        
        // 游戏界面
        this.elements.roomId = document.getElementById('room-id');
        this.elements.gameStatus = document.getElementById('game-status');
        this.elements.currentTurn = document.getElementById('current-turn');
        this.elements.turnTimer = document.getElementById('turn-timer');
        
        // 玩家区域
        this.elements.opponentPlayers = [
            document.getElementById('player-1'),
            document.getElementById('player-2'),
            document.getElementById('player-3')
        ];
        
        // 游戏中央区域
        this.elements.drawDeck = document.getElementById('draw-deck');
        this.elements.topCard = document.getElementById('top-card');
        this.elements.deckCount = document.getElementById('deck-count');
        this.elements.currentColor = document.getElementById('current-color');
        this.elements.directionIndicator = document.getElementById('direction-indicator');
        
        // 玩家手牌区域
        this.elements.yourName = document.getElementById('your-name');
        this.elements.yourCardCount = document.getElementById('your-card-count');
        this.elements.playerHand = document.getElementById('player-hand');
        this.elements.playHint = document.getElementById('play-hint');
        
        // 操作按钮
        this.elements.drawCardBtn = document.getElementById('draw-card-btn');
        this.elements.unoBtn = document.getElementById('uno-btn');
        
        // 模态框
        this.elements.colorPickerModal = document.getElementById('color-picker-modal');
        this.elements.gameOverModal = document.getElementById('game-over-modal');
        this.elements.rulesModal = document.getElementById('rules-modal');
        this.elements.notification = document.getElementById('notification');
        
        // 控制按钮
        this.elements.rulesBtn = document.getElementById('rules-btn');
        this.elements.closeRules = document.getElementById('close-rules');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 开始游戏
        this.elements.startGameBtn.addEventListener('click', () => this.startGame());
        
        // 游戏操作按钮
        this.elements.drawCardBtn.addEventListener('click', () => this.drawCard());
        this.elements.unoBtn.addEventListener('click', () => this.callUno());
        
        // 抽牌堆点击
        this.elements.drawDeck.addEventListener('click', () => this.drawCard());
        
        // 颜色选择
        document.querySelectorAll('.color-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const color = e.target.dataset.color;
                this.selectColor(color);
            });
        });
        
        // 规则说明
        this.elements.rulesBtn.addEventListener('click', () => this.showRules());
        this.elements.closeRules.addEventListener('click', () => this.hideRules());
        
        // 游戏结束后的操作
        document.getElementById('new-game-btn')?.addEventListener('click', () => this.restartGame());
        document.getElementById('exit-game-btn')?.addEventListener('click', () => this.exitGame());
        
        // 通知关闭
        document.getElementById('close-notification')?.addEventListener('click', () => this.hideNotification());
        
        // Enter键快捷操作
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                if (!this.elements.startScreen.classList.contains('hidden')) {
                    this.startGame();
                }
            }
        });
        
        // 模态框点击外部关闭
        [this.elements.colorPickerModal, this.elements.gameOverModal, this.elements.rulesModal].forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.add('hidden');
                }
            });
        });
    }

    /**
     * 连接WebSocket服务器
     */
    connectToServer() {
        try {
            this.socket = io();
            
            // 连接事件
            this.socket.on('connect', () => {
                console.log('WebSocket连接成功');
                this.connected = true;
                this.hideLoadingScreen();
            });
            
            this.socket.on('disconnect', () => {
                console.log('WebSocket连接断开');
                this.connected = false;
                this.showNotification('连接已断开，正在重新连接...', 'error');
            });
            
            // 游戏事件监听
            this.socket.on('connected', (data) => {
                console.log('服务器连接确认:', data);
                this.showNotification(data.message, 'success');
            });
            
            this.socket.on('game_created', (data) => {
                console.log('游戏创建成功:', data);
                this.playerInfo = {
                    playerId: data.player_id,
                    roomId: data.room_id
                };
                this.gameState = data.game_state;
                this.switchToGameScreen();
                this.updateGameUI();
                this.showNotification(data.message, 'success');
            });
            
            this.socket.on('game_updated', (data) => {
                console.log('游戏状态更新:', data);
                this.gameState = data.game_state;
                this.updateGameUI();
                if (data.message) {
                    this.showNotification(data.message, 'info');
                }
            });
            
            this.socket.on('game_over', (data) => {
                console.log('游戏结束:', data);
                this.showGameOverModal(data);
            });
            
            this.socket.on('error', (data) => {
                console.error('服务器错误:', data);
                this.showNotification(data.message, 'error');
            });
            
            this.socket.on('player_disconnected', (data) => {
                console.log('玩家离开:', data);
                this.showNotification(data.message, 'warning');
            });
            
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.showNotification('无法连接到游戏服务器', 'error');
        }
    }

    /**
     * 开始游戏
     */
    startGame() {
        const playerName = this.elements.playerNameInput.value.trim() || '玩家';
        
        if (!this.connected) {
            this.showNotification('正在连接服务器，请稍候...', 'warning');
            return;
        }
        
        this.socket.emit('create_game', {
            player_name: playerName
        });
        
        this.elements.startGameBtn.disabled = true;
        this.elements.startGameBtn.textContent = '创建中...';
    }

    /**
     * 抽牌
     */
    drawCard() {
        if (!this.isYourTurn()) {
            this.showNotification('现在不是您的回合', 'warning');
            return;
        }
        
        this.socket.emit('draw_card', {});
        this.elements.drawCardBtn.disabled = true;
    }

    /**
     * 叫UNO
     */
    callUno() {
        this.socket.emit('call_uno', {});
        this.showNotification('UNO! 🔥', 'success');
    }

    /**
     * 出牌
     */
    playCard(cardIndex) {
        if (!this.isYourTurn()) {
            this.showNotification('现在不是您的回合', 'warning');
            return;
        }
        
        const card = this.gameState.your_hand[cardIndex];
        if (!card || !card.can_play) {
            this.showNotification('这张牌无法出牌', 'warning');
            return;
        }
        
        // 检查是否是万能牌，需要选择颜色
        if (card.type === 'wild' || card.type === 'wild_draw_four') {
            this.selectedCard = cardIndex;
            this.showColorPicker();
            return;
        }
        
        // 普通牌直接出牌
        this.socket.emit('play_card', {
            card_index: cardIndex
        });
        
        this.clearSelectedCard();
    }

    /**
     * 选择颜色 (万能牌)
     */
    selectColor(color) {
        if (this.selectedCard === null) {
            this.showNotification('请先选择一张万能牌', 'warning');
            return;
        }
        
        this.socket.emit('play_card', {
            card_index: this.selectedCard,
            chosen_color: color
        });
        
        this.hideColorPicker();
        this.clearSelectedCard();
    }

    /**
     * 更新游戏UI
     */
    updateGameUI() {
        if (!this.gameState) return;
        
        // 更新房间信息
        this.elements.roomId.textContent = `房间: ${this.playerInfo.roomId.slice(0, 8)}...`;
        this.elements.gameStatus.textContent = this.gameState.game_over ? '游戏结束' : '游戏进行中';
        this.elements.currentTurn.textContent = `当前回合: ${this.gameState.current_player}`;
        
        // 更新回合指示器
        this.updateTurnIndicator();
        
        // 更新对手玩家信息
        this.updateOpponentPlayers();
        
        // 更新中央区域
        this.updateCenterArea();
        
        // 更新玩家手牌
        this.updatePlayerHand();
        
        // 更新操作按钮状态
        this.updateActionButtons();
        
        // 更新游戏方向
        this.updateDirection();
    }

    /**
     * 更新回合指示器
     */
    updateTurnIndicator() {
        const isYourTurn = this.isYourTurn();
        this.elements.turnTimer.style.display = isYourTurn ? 'block' : 'none';
        
        if (isYourTurn) {
            this.elements.playHint.classList.remove('hidden');
        } else {
            this.elements.playHint.classList.add('hidden');
        }
    }

    /**
     * 更新对手玩家信息
     */
    updateOpponentPlayers() {
        const opponents = this.gameState.players.filter(p => !p.is_you);
        
        opponents.forEach((player, index) => {
            if (index < this.elements.opponentPlayers.length) {
                const playerElement = this.elements.opponentPlayers[index];
                
                // 更新玩家名称
                const nameElement = playerElement.querySelector('.player-name');
                if (nameElement) nameElement.textContent = player.name;
                
                // 更新手牌数量
                const countElement = playerElement.querySelector('.card-count');
                if (countElement) countElement.textContent = `${player.hand_count}张牌`;
                
                // 更新状态
                const statusElement = playerElement.querySelector('.player-status');
                if (statusElement) {
                    let status = '';
                    if (player.has_called_uno) status = 'UNO!';
                    statusElement.textContent = status;
                }
                
                // 当前回合高亮
                if (player.is_current) {
                    playerElement.classList.add('current-turn');
                } else {
                    playerElement.classList.remove('current-turn');
                }
            }
        });
    }

    /**
     * 更新中央区域
     */
    updateCenterArea() {
        // 更新顶牌
        if (this.gameState.top_card) {
            this.updateTopCard(this.gameState.top_card);
        }
        
        // 更新抽牌堆数量
        this.elements.deckCount.textContent = this.gameState.draw_pile_count;
        
        // 更新当前颜色
        const currentColor = this.gameState.current_color;
        if (currentColor) {
            const colorName = this.getChineseColorName(currentColor);
            this.elements.currentColor.innerHTML = `当前颜色: <span style="color: ${this.getColorHex(colorName)}">${colorName}</span>`;
        }
    }

    /**
     * 更新顶牌显示
     */
    updateTopCard(topCard) {
        const cardElement = this.elements.topCard;
        const colorElement = cardElement.querySelector('.card-color');
        const numberElement = cardElement.querySelector('.card-number');
        
        // 设置卡牌颜色样式
        cardElement.className = 'game-card';
        cardElement.classList.add(`card-${topCard.color.toLowerCase()}`);
        
        // 更新卡牌内容 - 使用中文名称
        if (numberElement) {
            numberElement.textContent = topCard.display; // 直接使用后端发送的中文名称
        }
    }

    /**
     * 更新玩家手牌
     */
    updatePlayerHand() {
        const handContainer = this.elements.playerHand;
        handContainer.innerHTML = '';
        
        // 更新手牌数量显示
        this.elements.yourCardCount.textContent = `${this.gameState.your_hand.length}张牌`;
        
        // 生成手牌元素
        this.gameState.your_hand.forEach((card, index) => {
            const cardElement = this.createHandCardElement(card, index);
            handContainer.appendChild(cardElement);
        });
        
        // 检查是否需要显示UNO按钮
        if (this.gameState.your_hand.length === 1) {
            this.elements.unoBtn.disabled = false;
            this.elements.unoBtn.classList.add('bounce');
        } else {
            this.elements.unoBtn.disabled = true;
            this.elements.unoBtn.classList.remove('bounce');
        }
    }

    /**
     * 创建手牌元素
     */
    createHandCardElement(card, index) {
        const cardElement = document.createElement('div');
        cardElement.className = 'hand-card';
        
        // 添加颜色样式
        cardElement.classList.add(`card-${card.color.toLowerCase()}`);
        
        // 可出牌提示
        if (card.can_play) {
            cardElement.classList.add('playable');
        }
        
        // 卡牌内容 - 使用中文名称
        cardElement.innerHTML = `
            <div class="card-content">
                <div class="card-color"></div>
                <div class="card-number">${card.card}</div>
            </div>
        `;
        
        // 点击事件
        cardElement.addEventListener('click', () => {
            if (card.can_play) {
                this.selectHandCard(cardElement, index);
            } else {
                this.showNotification('这张牌无法出牌', 'warning');
            }
        });
        
        return cardElement;
    }

    /**
     * 选择手牌
     */
    selectHandCard(cardElement, index) {
        // 清除之前的选择
        document.querySelectorAll('.hand-card.selected').forEach(el => {
            el.classList.remove('selected');
        });
        
        // 选择当前卡牌
        cardElement.classList.add('selected');
        this.selectedCard = index;
        
        // 添加确认出牌的延迟，给用户查看的时间
        setTimeout(() => {
            this.playCard(index);
        }, 300);
    }

    /**
     * 更新操作按钮状态
     */
    updateActionButtons() {
        const isYourTurn = this.isYourTurn();
        
        // 抽牌按钮
        this.elements.drawCardBtn.disabled = !isYourTurn;
        
        // UNO按钮 (已在updatePlayerHand中处理)
        
        // 跳过按钮已移除 - 不符合标准UNO规则
    }

    /**
     * 更新游戏方向指示器
     */
    updateDirection() {
        const directionElement = this.elements.directionIndicator.querySelector('.direction-arrow');
        const textElement = this.elements.directionIndicator.querySelector('span');
        
        if (this.gameState.direction === '顺时针') {
            directionElement.textContent = '↻';
            directionElement.className = 'direction-arrow clockwise';
            textElement.textContent = '顺时针';
        } else if (this.gameState.direction === '逆时针') {
            directionElement.textContent = '↺';
            directionElement.className = 'direction-arrow counterclockwise';
            textElement.textContent = '逆时针';
        }
    }

    /**
     * 工具方法
     */
    isYourTurn() {
        return this.gameState && this.gameState.is_your_turn;
    }

    getChineseColorName(englishColor) {
        const colorMap = {
            'red': '红',
            'blue': '蓝',
            'green': '绿',
            'yellow': '黄',
            'black': '黑'
        };
        return colorMap[englishColor] || englishColor;
    }

    getColorHex(colorName) {
        const colorMap = {
            '红': '#e53e3e',
            '蓝': '#3182ce',
            '绿': '#38a169',
            '黄': '#d69e2e',
            '黑': '#2d3748'
        };
        return colorMap[colorName] || '#2d3748';
    }

    getCardDisplayText(cardType) {
        // 这个函数现在不再需要，因为我们直接使用后端发送的中文名称
        // 保留作为备用
        const typeMap = {
            'number': '数字',
            'skip': '禁止',
            'reverse': '反转', 
            'draw_two': '罚2',
            'wild': '万能',
            'wild_draw_four': '万能罚4'
        };
        
        return typeMap[cardType] || cardType;
    }

    clearSelectedCard() {
        this.selectedCard = null;
        document.querySelectorAll('.hand-card.selected').forEach(el => {
            el.classList.remove('selected');
        });
    }

    /**
     * UI切换方法
     */
    hideLoadingScreen() {
        this.elements.loadingScreen.classList.add('fade-out');
        setTimeout(() => {
            this.elements.loadingScreen.classList.add('hidden');
        }, 500);
    }

    switchToGameScreen() {
        this.elements.startScreen.classList.add('fade-out');
        setTimeout(() => {
            this.elements.startScreen.classList.add('hidden');
            this.elements.gameScreen.classList.remove('hidden');
            this.elements.gameScreen.classList.add('fade-in');
        }, 500);
    }

    showColorPicker() {
        this.elements.colorPickerModal.classList.remove('hidden');
        this.elements.colorPickerModal.classList.add('fade-in');
    }

    hideColorPicker() {
        this.elements.colorPickerModal.classList.add('fade-out');
        setTimeout(() => {
            this.elements.colorPickerModal.classList.add('hidden');
            this.elements.colorPickerModal.classList.remove('fade-out');
        }, 300);
    }

    showGameOverModal(data) {
        const modal = this.elements.gameOverModal;
        
        // 更新获胜者信息
        const winnerElement = document.getElementById('winner-name');
        if (winnerElement) {
            winnerElement.textContent = data.winner;
        }
        
        // 更新得分榜
        const scoresElement = document.getElementById('scores-list');
        if (scoresElement && data.scores) {
            scoresElement.innerHTML = '';
            Object.entries(data.scores).forEach(([name, score]) => {
                const scoreItem = document.createElement('div');
                scoreItem.className = 'score-item';
                scoreItem.innerHTML = `<span>${name}</span><span>${score}分</span>`;
                scoresElement.appendChild(scoreItem);
            });
        }
        
        modal.classList.remove('hidden');
        modal.classList.add('fade-in');
    }

    showRules() {
        this.elements.rulesModal.classList.remove('hidden');
        this.elements.rulesModal.classList.add('fade-in');
    }

    hideRules() {
        this.elements.rulesModal.classList.add('fade-out');
        setTimeout(() => {
            this.elements.rulesModal.classList.add('hidden');
            this.elements.rulesModal.classList.remove('fade-out');
        }, 300);
    }

    showNotification(message, type = 'info') {
        const notification = this.elements.notification;
        const textElement = document.getElementById('notification-text');
        
        if (textElement) {
            textElement.textContent = message;
        }
        
        // 设置通知类型样式
        notification.className = 'notification';
        notification.classList.add(type);
        notification.classList.remove('hidden');
        notification.classList.add('fade-in');
        
        // 自动隐藏 (错误消息不自动隐藏)
        if (type !== 'error') {
            setTimeout(() => {
                this.hideNotification();
            }, 3000);
        }
    }

    hideNotification() {
        const notification = this.elements.notification;
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.classList.add('hidden');
            notification.classList.remove('fade-out');
        }, 300);
    }

    /**
     * 游戏控制方法
     */
    restartGame() {
        location.reload();
    }

    exitGame() {
        if (this.socket) {
            this.socket.disconnect();
        }
        location.reload();
    }
}

// 页面加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    console.log('🎮 UNO游戏客户端启动');
    window.unoGame = new UnoGameClient();
});

// 页面卸载时断开连接
window.addEventListener('beforeunload', () => {
    if (window.unoGame && window.unoGame.socket) {
        window.unoGame.socket.disconnect();
    }
}); 