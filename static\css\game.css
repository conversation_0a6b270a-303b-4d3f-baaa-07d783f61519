/* UNO游戏 - 卡通风格样式表 */

/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* UNO经典颜色 */
    --uno-red: #e53e3e;
    --uno-blue: #3182ce;
    --uno-green: #38a169;
    --uno-yellow: #d69e2e;
    --uno-black: #2d3748;
    
    /* 背景渐变 */
    --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-secondary: linear-gradient(45deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    --bg-card: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%);
    
    /* 阴影效果 */
    --shadow-light: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 8px 15px rgba(0, 0, 0, 0.2);
    --shadow-heavy: 0 15px 30px rgba(0, 0, 0, 0.3);
    
    /* 字体 */
    --font-primary: '<PERSON>oka One', cursive;
    --font-secondary: 'Comic Neue', cursive;
}

body {
    font-family: var(--font-secondary);
    background: var(--bg-primary);
    min-height: 100vh;
    overflow-x: hidden;
    user-select: none;
}

/* 工具类 */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.5s ease-in-out;
}

.bounce {
    animation: bounce 0.6s ease-in-out;
}

.wobble {
    animation: wobble 0.8s ease-in-out;
}

/* 加载屏幕 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: white;
}

.uno-logo {
    font-family: var(--font-primary);
    font-size: 4rem;
    margin-bottom: 2rem;
    animation: pulse 2s infinite;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* 开始屏幕 */
.start-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-secondary);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.start-container {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: var(--shadow-heavy);
    max-width: 500px;
    width: 90%;
}

.game-title {
    font-family: var(--font-primary);
    font-size: 4rem;
    margin-bottom: 1rem;
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.title-u { color: var(--uno-red); }
.title-n { color: var(--uno-blue); }
.title-o { color: var(--uno-green); }

.subtitle {
    font-size: 1.2rem;
    color: var(--uno-yellow);
    margin-top: 0.5rem;
}

.player-setup {
    margin: 2rem 0;
}

.player-setup label {
    display: block;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--uno-black);
}

.player-setup input {
    width: 100%;
    padding: 1rem;
    border: 3px solid var(--uno-blue);
    border-radius: 15px;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    text-align: center;
    font-family: var(--font-secondary);
}

.start-btn {
    background: linear-gradient(45deg, var(--uno-red), var(--uno-yellow));
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.3rem;
    font-family: var(--font-primary);
    border-radius: 15px;
    cursor: pointer;
    box-shadow: var(--shadow-medium);
    transition: transform 0.2s ease;
}

.start-btn:hover {
    transform: scale(1.05);
}

.start-btn:active {
    transform: scale(0.95);
}

.game-info {
    margin-top: 2rem;
    color: var(--uno-black);
}

.game-info p {
    margin: 0.5rem 0;
    font-size: 1.1rem;
}

/* 主游戏界面 */
.game-screen {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 游戏头部 */
.game-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1rem;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.game-info-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    flex-wrap: wrap;
}

.room-info, .turn-indicator {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.turn-timer {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--uno-green);
    animation: pulse 1s infinite;
}

/* 对手玩家区域 */
.opponents-area {
    position: relative;
    height: 200px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1rem;
}

.opponent-player {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.opponent-player.current-turn {
    border-color: var(--uno-yellow);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 20px rgba(255, 255, 0, 0.5);
}

.player-avatar {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: var(--bg-card);
}

.player-info {
    text-align: center;
    color: white;
}

.player-name {
    font-weight: bold;
    font-size: 1.1rem;
}

.card-count {
    font-size: 0.9rem;
    opacity: 0.8;
}

.player-status {
    font-size: 0.8rem;
    height: 1rem;
}

.hand-preview {
    display: flex;
    gap: -10px;
}

.card-back-stack {
    width: 40px;
    height: 60px;
    background: linear-gradient(145deg, #4a5568, #2d3748);
    border-radius: 8px;
    position: relative;
    border: 2px solid white;
}

.card-back-stack::before,
.card-back-stack::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, #4a5568, #2d3748);
    border-radius: 8px;
    border: 2px solid white;
}

.card-back-stack::before {
    top: -3px;
    left: -3px;
    z-index: -1;
}

.card-back-stack::after {
    top: -6px;
    left: -6px;
    z-index: -2;
}

/* 中央游戏区域 */
.game-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    padding: 2rem;
}

.deck-area {
    display: flex;
    gap: 3rem;
    align-items: center;
}

.draw-pile, .discard-pile {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.card-deck, .game-card {
    width: 120px;
    height: 180px;
    border-radius: 15px;
    cursor: pointer;
    transition: transform 0.2s ease;
    position: relative;
}

.card-deck:hover, .game-card:hover {
    transform: translateY(-5px);
}

.card-back {
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, #4a5568, #2d3748);
    border-radius: 15px;
    border: 3px solid white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: yellow;
}

.card-back::before {
    content: '🎴';
    font-size: 3rem;
    color: yellow;
}

.game-card {
    background: var(--bg-card);
    border: 3px solid white;
    box-shadow: var(--shadow-medium);
}

.card-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.card-color {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 1rem;
}

.card-number {
    font-family: var(--font-primary);
    font-size: 1.2rem;
    font-weight: bold;
    color: yellow;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* 卡牌颜色样式 */
.card-red .card-color { background: var(--uno-red); }
.card-blue .card-color { background: var(--uno-blue); }
.card-green .card-color { background: var(--uno-green); }
.card-yellow .card-color { background: var(--uno-yellow); }
.card-black .card-color, .card-wild .card-color { 
    background: linear-gradient(45deg, var(--uno-red), var(--uno-blue), var(--uno-green), var(--uno-yellow));
}

/* 万能牌特殊样式 */
.card-black, .card-wild {
    background: linear-gradient(135deg, #2d3748, #4a5568);
    border: 3px solid #ffffff;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.deck-label, .pile-label {
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
}

.deck-count {
    position: absolute;
    top: -10px;
    right: -10px;
    background: var(--uno-red);
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}

.current-color-indicator {
    color: white;
    text-align: center;
    font-weight: bold;
}

.direction-indicator {
    position: absolute;
    top: 1rem;
    right: 2rem;
    color: white;
    text-align: center;
    background: rgba(0, 0, 0, 0.7);
    padding: 1.2rem;
    border-radius: 15px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    font-weight: bold;
    min-width: 100px;
}

.direction-arrow {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    display: block;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* 顺时针旋转动画 */
.direction-arrow.clockwise {
    animation: rotateClockwise 2s linear infinite;
    color: #4ade80; /* 绿色表示顺时针 */
}

/* 逆时针旋转动画 */
.direction-arrow.counterclockwise {
    animation: rotateCounterclockwise 2s linear infinite;
    color: #f87171; /* 红色表示逆时针 */
}

.direction-indicator span {
    font-size: 1rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

/* 玩家手牌区域 */
.player-hand-area {
    background: rgba(0, 0, 0, 0.1);
    padding: 1rem;
    border-top: 2px solid rgba(255, 255, 255, 0.2);
}

.hand-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.your-name {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    color: white;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-family: var(--font-secondary);
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-light);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-btn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.draw-btn {
    background: var(--uno-blue);
    color: white;
}

.uno-btn {
    background: var(--uno-red);
    color: white;
    animation: pulse 1.5s infinite;
}

.skip-btn {
    background: var(--uno-yellow);
    color: var(--uno-black);
}

.player-hand {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
    min-height: 200px;
    align-items: flex-end;
    overflow-x: auto;
    padding: 1rem 0;
}

.hand-card {
    width: 80px;
    height: 120px;
    border-radius: 12px;
    background: var(--bg-card);
    border: 2px solid white;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    flex-shrink: 0;
}

.hand-card:hover {
    transform: translateY(-20px) scale(1.1);
    z-index: 10;
}

.hand-card.playable {
    border-color: var(--uno-green);
    box-shadow: 0 0 15px rgba(56, 161, 105, 0.5);
}

.hand-card.selected {
    transform: translateY(-30px) scale(1.15);
    border-color: var(--uno-yellow);
    box-shadow: 0 0 20px rgba(214, 158, 46, 0.8);
}

.hand-card .card-content {
    padding: 0.5rem;
}

.hand-card .card-color {
    width: 50px;
    height: 50px;
}

.hand-card .card-number {
    font-size: 0.9rem;
    color: yellow(0, 0, 0);
}

.play-hint {
    text-align: center;
    color: white;
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

/* 游戏控制面板 */
.game-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.control-btn {
    padding: 0.8rem;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: var(--shadow-medium);
    cursor: pointer;
    transition: transform 0.2s ease;
    font-size: 1.2rem;
}

.control-btn:hover {
    transform: scale(1.1);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    text-align: center;
    box-shadow: var(--shadow-heavy);
}

.modal-content h2, .modal-content h3 {
    color: var(--uno-black);
    margin-bottom: 1rem;
}

.color-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin: 2rem 0;
}

.color-btn {
    padding: 1rem;
    border: none;
    border-radius: 15px;
    font-size: 1.2rem;
    font-family: var(--font-secondary);
    cursor: pointer;
    transition: transform 0.2s ease;
    color: white;
    font-weight: bold;
}

.color-btn:hover {
    transform: scale(1.05);
}

.color-btn.red { background: var(--uno-red); }
.color-btn.blue { background: var(--uno-blue); }
.color-btn.green { background: var(--uno-green); }
.color-btn.yellow { background: var(--uno-yellow); }

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.scores-list {
    text-align: left;
    margin: 1rem 0;
}

.score-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    margin: 0.5rem 0;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
}

.rules-content {
    text-align: left;
    line-height: 1.6;
}

.rules-content ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.rules-content li {
    margin: 0.5rem 0;
}

/* 通知提示 */
.notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--uno-black);
    color: white;
    padding: 1rem 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow-medium);
    z-index: 3000;
    max-width: 90%;
}

.notification-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 动画定义 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-20px); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes wobble {
    0% { transform: rotate(0deg); }
    15% { transform: rotate(-5deg); }
    30% { transform: rotate(4deg); }
    45% { transform: rotate(-3deg); }
    60% { transform: rotate(2deg); }
    75% { transform: rotate(-1deg); }
    100% { transform: rotate(0deg); }
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes rotateClockwise {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes rotateCounterclockwise {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(-360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-title {
        font-size: 3rem;
    }
    
    .opponents-area {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
        height: auto;
    }
    
    .deck-area {
        gap: 2rem;
    }
    
    .card-deck, .game-card {
        width: 100px;
        height: 150px;
    }
    
    .hand-card {
        width: 70px;
        height: 105px;
    }
    
    .action-buttons {
        justify-content: center;
        gap: 0.5rem;
    }
    
    .action-btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
    
    .player-hand {
        gap: 5px;
    }
    
    .color-options {
        grid-template-columns: 1fr;
    }
    
    .direction-indicator {
        top: 0.5rem;
        right: 0.5rem;
        padding: 0.8rem;
        min-width: 80px;
    }
    
    .direction-arrow {
        font-size: 2rem;
    }
    
    .direction-indicator span {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .start-container {
        padding: 2rem;
    }
    
    .game-title {
        font-size: 2.5rem;
    }
    
    .opponents-area {
        padding: 0.5rem;
        gap: 0.5rem;
    }
    
    .game-center {
        padding: 1rem;
    }
    
    .hand-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .game-controls {
        bottom: 10px;
        right: 10px;
    }
    
    .direction-indicator {
        top: 0.3rem;
        right: 0.3rem;
        padding: 0.6rem;
        min-width: 70px;
    }
    
    .direction-arrow {
        font-size: 1.5rem;
        margin-bottom: 0.3rem;
    }
    
    .direction-indicator span {
        font-size: 0.7rem;
    }
} 