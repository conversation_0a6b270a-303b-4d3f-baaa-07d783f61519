<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎴 UNO游戏 - 卡通风格</title>
    <link rel="stylesheet" href="/static/css/game.css">
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&family=Comic+Neue:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
</head>
<body>
    <!-- 游戏加载遮罩 -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="uno-logo">🎴 UNO</div>
            <div class="loading-spinner"></div>
            <p>正在连接游戏服务器...</p>
        </div>
    </div>

    <!-- 游戏开始界面 -->
    <div id="start-screen" class="start-screen">
        <div class="start-container">
            <h1 class="game-title">
                <span class="title-u">U</span>
                <span class="title-n">N</span>
                <span class="title-o">O</span>
                <div class="subtitle">卡通风格版</div>
            </h1>
            <div class="player-setup">
                <label for="player-name">输入您的昵称:</label>
                <input type="text" id="player-name" placeholder="请输入昵称" maxlength="10" value="玩家">
                <button id="start-game-btn" class="start-btn">🎯 开始游戏</button>
            </div>
            <div class="game-info">
                <p>🤖 将与3个AI机器人对战</p>
                <p>🎴 标准UNO规则，先出完牌者获胜！</p>
            </div>
        </div>
    </div>

    <!-- 主游戏界面 -->
    <div id="game-screen" class="game-screen hidden">
        <!-- 游戏头部信息 -->
        <div class="game-header">
            <div class="game-info-panel">
                <div class="room-info">
                    <span id="room-id">房间: 未连接</span>
                    <span id="game-status">等待开始...</span>
                </div>
                <div class="turn-indicator">
                    <span id="current-turn">当前回合: 无</span>
                    <div id="turn-timer" class="turn-timer"></div>
                </div>
            </div>
        </div>

        <!-- 对手玩家区域 -->
        <div class="opponents-area">
            <!-- AI玩家1 (顶部左) -->
            <div id="player-1" class="opponent-player player-area">
                <div class="player-avatar">🤖</div>
                <div class="player-info">
                    <div class="player-name">小智</div>
                    <div class="card-count">7张牌</div>
                    <div class="player-status"></div>
                </div>
                <div class="hand-preview">
                    <div class="card-back-stack"></div>
                </div>
            </div>

            <!-- AI玩家2 (顶部右) -->
            <div id="player-2" class="opponent-player player-area">
                <div class="player-avatar">🤖</div>
                <div class="player-info">
                    <div class="player-name">小慧</div>
                    <div class="card-count">7张牌</div>
                    <div class="player-status"></div>
                </div>
                <div class="hand-preview">
                    <div class="card-back-stack"></div>
                </div>
            </div>

            <!-- AI玩家3 (右侧) -->
            <div id="player-3" class="opponent-player player-area">
                <div class="player-avatar">🤖</div>
                <div class="player-info">
                    <div class="player-name">小强</div>
                    <div class="card-count">7张牌</div>
                    <div class="player-status"></div>
                </div>
                <div class="hand-preview">
                    <div class="card-back-stack"></div>
                </div>
            </div>
        </div>

        <!-- 中央游戏区域 -->
        <div class="game-center">
            <!-- 牌组区域 -->
            <div class="deck-area">
                <!-- 抽牌堆 -->
                <div class="draw-pile">
                    <div id="draw-deck" class="card-deck">
                        <div class="card-back"></div>
                        <div class="deck-label">抽牌堆</div>
                        <div id="deck-count" class="deck-count">108</div>
                    </div>
                </div>

                <!-- 弃牌堆/当前牌 -->
                <div class="discard-pile">
                    <div id="top-card" class="game-card">
                        <div class="card-content">
                            <div class="card-color"></div>
                            <div class="card-number">?</div>
                        </div>
                    </div>
                    <div class="pile-label">当前牌</div>
                    <div id="current-color" class="current-color-indicator">
                        当前颜色: <span>未知</span>
                    </div>
                </div>
            </div>

            <!-- 游戏方向指示器 -->
            <div id="direction-indicator" class="direction-indicator">
                <div class="direction-arrow">↻</div>
                <span>顺时针</span>
            </div>
        </div>

        <!-- 玩家手牌区域 -->
        <div class="player-hand-area">
            <div class="hand-header">
                <div class="player-info">
                    <span id="your-name" class="your-name">您</span>
                    <span id="your-card-count" class="card-count">7张牌</span>
                </div>
                <div class="action-buttons">
                    <button id="draw-card-btn" class="action-btn draw-btn" disabled>
                        🎴 抽牌
                    </button>
                    <button id="uno-btn" class="action-btn uno-btn" disabled>
                        🔥 UNO!
                    </button>
                </div>
            </div>
            
            <!-- 手牌容器 -->
            <div id="player-hand" class="player-hand">
                <!-- 手牌将在这里动态生成 -->
            </div>
            
            <!-- 出牌提示 -->
            <div id="play-hint" class="play-hint hidden">
                <p>请选择一张牌出牌，或点击抽牌按钮</p>
            </div>
        </div>

        <!-- 游戏操作面板 -->
        <div class="game-controls">
            <button id="rules-btn" class="control-btn">📋 规则</button>
        </div>
    </div>

    <!-- 颜色选择模态框 -->
    <div id="color-picker-modal" class="modal hidden">
        <div class="modal-content">
            <h3>选择颜色</h3>
            <p>请为万能牌选择一个颜色:</p>
            <div class="color-options">
                <button class="color-btn red" data-color="红">🔴 红色</button>
                <button class="color-btn blue" data-color="蓝">🔵 蓝色</button>
                <button class="color-btn green" data-color="绿">🟢 绿色</button>
                <button class="color-btn yellow" data-color="黄">🟡 黄色</button>
            </div>
        </div>
    </div>

    <!-- 游戏结束模态框 -->
    <div id="game-over-modal" class="modal hidden">
        <div class="modal-content">
            <h2>🎊 游戏结束</h2>
            <div id="winner-info">
                <p>获胜者: <span id="winner-name">未知</span></p>
            </div>
            <div id="score-board">
                <h3>📊 得分榜</h3>
                <div id="scores-list"></div>
            </div>
            <div class="modal-actions">
                <button id="new-game-btn" class="action-btn">🔄 再来一局</button>
                <button id="exit-game-btn" class="action-btn">🚪 退出游戏</button>
            </div>
        </div>
    </div>

    <!-- 通知提示 -->
    <div id="notification" class="notification hidden">
        <div class="notification-content">
            <span id="notification-text">通知消息</span>
            <button id="close-notification" class="close-btn">✕</button>
        </div>
    </div>

    <!-- 规则说明模态框 -->
    <div id="rules-modal" class="modal hidden">
        <div class="modal-content">
            <h2>📋 UNO游戏规则</h2>
            <div class="rules-content">
                <h3>🎯 游戏目标</h3>
                <p>率先出完手中所有牌的玩家获胜！</p>
                
                <h3>🎴 特殊牌型</h3>
                <ul>
                    <li><strong>跳过牌</strong> - 下一位玩家跳过回合</li>
                    <li><strong>反转牌</strong> - 游戏方向反转</li>
                    <li><strong>+2牌</strong> - 下一位玩家抽2张牌并跳过</li>
                    <li><strong>万能牌</strong> - 可指定下一张牌的颜色</li>
                    <li><strong>万能+4</strong> - 指定颜色且下一位玩家抽4张</li>
                </ul>
                
                <h3>🔥 UNO规则</h3>
                <p>当手中只剩1张牌时，必须喊"UNO!"，否则被发现后要抽2张牌。</p>
                
                <h3>🎮 操作说明</h3>
                <ul>
                    <li>点击手牌出牌</li>
                    <li>点击抽牌堆抽牌</li>
                    <li>剩余1张牌时点击UNO按钮</li>
                </ul>
            </div>
            <button id="close-rules" class="action-btn">知道了</button>
        </div>
    </div>

    <script src="/static/js/game.js"></script>
</body>
</html> 