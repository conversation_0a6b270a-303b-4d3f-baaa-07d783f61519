# -*- coding: utf-8 -*-
"""
UNO游戏主程序
包含Flask服务器和Socket.IO通信
"""

from flask import Flask, render_template, request
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
import uuid
import asyncio
import threading
from game_logic.game_manager import UnoGameManager
from game_logic.card import CardColor, CardType
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'uno_game_secret_key_2024'
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# 全局游戏管理器
game_managers = {}  # room_id -> UnoGameManager
player_sessions = {}  # session_id -> {room_id, player_id, socket_id}

@app.route('/')
def index():
    """主页 - 显示游戏界面"""
    return render_template('index.html')

@socketio.on('connect')
def handle_connect():
    """客户端连接事件"""
    session_id = str(uuid.uuid4())
    player_sessions[request.sid] = {
        'session_id': session_id,
        'room_id': None,
        'player_id': None,
        'socket_id': request.sid
    }
    
    emit('connected', {
        'session_id': session_id,
        'message': '连接成功，欢迎来到UNO游戏！'
    })
    
    print(f"新客户端连接: {request.sid}, session: {session_id}")

@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接事件"""
    if request.sid in player_sessions:
        session_info = player_sessions[request.sid]
        room_id = session_info.get('room_id')
        
        if room_id and room_id in game_managers:
            # 通知房间内其他玩家有人离开
            emit('player_disconnected', {
                'message': '有玩家离开了游戏',
                'player_id': session_info.get('player_id')
            }, room=room_id)
        
        del player_sessions[request.sid]
        print(f"客户端断开连接: {request.sid}")

@socketio.on('create_game')
def handle_create_game(data):
    """创建新游戏房间"""
    try:
        room_id = str(uuid.uuid4())
        player_name = data.get('player_name', '您')
        
        # 创建游戏管理器
        game_manager = UnoGameManager()
        
        # 添加真实玩家
        human_player_id = game_manager.add_human_player(player_name)
        
        # 添加3个AI玩家
        game_manager.add_ai_player("小智", "medium")
        game_manager.add_ai_player("小慧", "hard") 
        game_manager.add_ai_player("小强", "easy")
        
        # 初始化游戏
        start_result = game_manager.start_game()
        
        if not start_result['success']:
            emit('error', {'message': f'游戏启动失败: {start_result["message"]}'})
            return
        
        # 保存游戏管理器
        game_managers[room_id] = game_manager
        
        # 更新玩家会话信息
        if request.sid in player_sessions:
            player_sessions[request.sid].update({
                'room_id': room_id,
                'player_id': human_player_id
            })
        
        # 加入房间
        join_room(room_id)
        
        # 获取游戏状态
        game_state = get_game_state(game_manager, human_player_id)
        
        emit('game_created', {
            'room_id': room_id,
            'player_id': human_player_id,
            'message': f'游戏房间创建成功！正在开始游戏...',
            'game_state': game_state
        })
        
        print(f"创建新游戏房间: {room_id}, 玩家: {player_name}")
        
        # 如果第一个玩家是AI，启动AI回合处理
        if game_manager.game.current_player != int(human_player_id):
            def start_ai_turns():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(process_ai_turns(game_manager, room_id))
                finally:
                    loop.close()
            
            ai_thread = threading.Thread(target=start_ai_turns)
            ai_thread.daemon = True
            ai_thread.start()
        
    except Exception as e:
        emit('error', {'message': f'创建游戏失败: {str(e)}'})
        print(f"创建游戏错误: {e}")

@socketio.on('play_card')
def handle_play_card(data):
    """处理玩家出牌"""
    try:
        session_info = player_sessions.get(request.sid)
        if not session_info:
            emit('error', {'message': '会话信息不存在'})
            return
            
        room_id = session_info['room_id']
        player_id = session_info['player_id']
        
        if room_id not in game_managers:
            emit('error', {'message': '游戏房间不存在'})
            return
            
        game_manager = game_managers[room_id]
        card_index = data.get('card_index')
        chosen_color = data.get('chosen_color')  # 万能牌选择的颜色
        
        # 转换颜色字符串为枚举 - 支持中文和英文
        color_enum = None
        if chosen_color:
            # 中文到枚举的映射
            chinese_color_map = {'红': CardColor.RED, '蓝': CardColor.BLUE, 
                        '绿': CardColor.GREEN, '黄': CardColor.YELLOW}
            # 英文到枚举的映射
            english_color_map = {'red': CardColor.RED, 'blue': CardColor.BLUE, 
                        'green': CardColor.GREEN, 'yellow': CardColor.YELLOW}
            
            color_enum = chinese_color_map.get(chosen_color) or english_color_map.get(chosen_color)
        
        # 执行出牌
        result = game_manager.play_card(player_id, card_index, color_enum)
        
        if result['success']:
            # 获取更新后的游戏状态
            game_state = get_game_state(game_manager, player_id)
            
            # 广播游戏状态给房间内所有玩家
            emit('game_updated', {
                'game_state': game_state,
                'action': 'play_card',
                'message': result.get('message', '出牌成功')
            }, room=room_id)
            
            # 检查游戏是否结束
            if game_manager.game.state.value == 'finished':
                winner_name = game_manager.players[game_manager.game.winner].name if game_manager.game.winner is not None else '无'
                emit('game_over', {
                    'winner': winner_name,
                    'scores': {p.name: game_manager.game.scores[i] for i, p in enumerate(game_manager.players)}
                }, room=room_id)
            else:
                # 启动AI回合处理线程
                def start_ai_turns():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        loop.run_until_complete(process_ai_turns(game_manager, room_id))
                    finally:
                        loop.close()
                
                ai_thread = threading.Thread(target=start_ai_turns)
                ai_thread.daemon = True
                ai_thread.start()
        else:
            emit('error', {'message': result.get('message', '出牌失败')})
            
    except Exception as e:
        emit('error', {'message': f'出牌处理错误: {str(e)}'})
        print(f"出牌处理错误: {e}")

@socketio.on('draw_card')
def handle_draw_card(data):
    """处理玩家抽牌"""
    try:
        session_info = player_sessions.get(request.sid)
        if not session_info:
            emit('error', {'message': '会话信息不存在'})
            return
            
        room_id = session_info['room_id']
        player_id = session_info['player_id']
        
        if room_id not in game_managers:
            emit('error', {'message': '游戏房间不存在'})
            return
            
        game_manager = game_managers[room_id]
        
        # 执行抽牌
        result = game_manager.draw_card(player_id)
        
        if result['success']:
            # 获取更新后的游戏状态
            game_state = get_game_state(game_manager, player_id)
            
            # 广播游戏状态
            emit('game_updated', {
                'game_state': game_state,
                'action': 'draw_card',
                'message': result.get('message', '抽牌成功')
            }, room=room_id)
            
            # 启动AI回合处理线程
            def start_ai_turns():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(process_ai_turns(game_manager, room_id))
                finally:
                    loop.close()
            
            ai_thread = threading.Thread(target=start_ai_turns)
            ai_thread.daemon = True
            ai_thread.start()
        else:
            emit('error', {'message': result.get('message', '抽牌失败')})
            
    except Exception as e:
        emit('error', {'message': f'抽牌处理错误: {str(e)}'})
        print(f"抽牌处理错误: {e}")

@socketio.on('call_uno')
def handle_call_uno(data):
    """处理玩家叫UNO"""
    try:
        session_info = player_sessions.get(request.sid)
        if not session_info:
            emit('error', {'message': '会话信息不存在'})
            return
            
        room_id = session_info['room_id']
        player_id = session_info['player_id']
        
        if room_id not in game_managers:
            emit('error', {'message': '游戏房间不存在'})
            return
            
        game_manager = game_managers[room_id]
        
        # 执行叫UNO
        result = game_manager.call_uno(player_id)
        
        # 获取更新后的游戏状态
        game_state = get_game_state(game_manager, player_id)
        
        # 广播结果
        emit('game_updated', {
            'game_state': game_state,
            'action': 'call_uno',
            'message': result.get('message', 'UNO!')
        }, room=room_id)
        
    except Exception as e:
        emit('error', {'message': f'叫UNO处理错误: {str(e)}'})
        print(f"叫UNO处理错误: {e}")

def get_game_state(game_manager, human_player_id):
    """获取游戏状态（适合发送给客户端）"""
    game = game_manager.game
    
    # 获取顶牌
    top_card = game.deck.get_top_card()
    if not top_card:
        return {}
    
    # 获取人类玩家索引
    human_player_index = int(human_player_id)
    if human_player_index >= len(game_manager.players):
        return {}
    
    human_player = game_manager.players[human_player_index]
    
    # 构建游戏状态
    state = {
        'current_player': game_manager.players[game.current_player].name,
        'current_player_id': str(game.current_player),
        'is_your_turn': game.current_player == human_player_index,
        'top_card': {
            'color': top_card.color.value,
            'type': top_card.card_type.value,
            'display': top_card.get_chinese_name(),
            'number': top_card.number
        },
        'current_color': game.chosen_color.value if game.chosen_color else top_card.color.value,
        'direction': '顺时针' if game.direction.value == 'clockwise' else '逆时针',
        'draw_pile_count': len(game.deck.draw_pile),
        'must_draw': game.must_draw,  # 保留这个用于前端逻辑
        'draw_penalty': game.draw_penalty,  # 保留这个用于前端逻辑
        'your_hand': [
            {
                'index': i,
                'card': card.get_chinese_name(),
                'color': card.color.value,
                'type': card.card_type.value,
                'number': card.number,
                'can_play': game.can_play_card(human_player_index, card)
            }
            for i, card in enumerate(human_player.hand)
        ],
        'players': [
            {
                'name': player.name,
                'player_id': str(i),
                'hand_count': len(player.hand),
                'is_current': i == game.current_player,
                'is_you': i == human_player_index,
                'has_called_uno': game.uno_declared[i],
                'score': game.scores[i]
            }
            for i, player in enumerate(game_manager.players)
        ],
        'game_over': game.state.value == 'finished',
        'winner': game_manager.players[game.winner].name if game.winner is not None else None
    }
    
    return state

async def process_ai_turns(game_manager, room_id):
    """处理AI玩家回合"""
    try:
        while game_manager.game.state.value == 'playing':
            current_player_idx = game_manager.game.current_player
            current_player = game_manager.players[current_player_idx]
            
            # 如果当前玩家是AI，处理AI回合
            if hasattr(current_player, 'difficulty'):  # AI玩家标识
                # 等待一小段时间模拟思考
                await asyncio.sleep(1.5)
                
                # 执行AI回合
                result = await game_manager.process_ai_turn()
                
                # 获取人类玩家ID（字符串格式）
                human_player_id = None
                for i, player in enumerate(game_manager.players):
                    if not hasattr(player, 'difficulty'):  # 人类玩家
                        human_player_id = str(i)
                        break
                
                if human_player_id and result:
                    game_state = get_game_state(game_manager, human_player_id)
                    
                    # 广播AI行动结果
                    socketio.emit('game_updated', {
                        'game_state': game_state,
                        'action': 'ai_action',
                        'message': result.get('message', f'{current_player.name} 行动完成')
                    }, room=room_id)
                
                # 检查游戏是否结束
                if game_manager.game.state.value == 'finished':
                    winner_name = game_manager.players[game_manager.game.winner].name if game_manager.game.winner is not None else '无'
                    socketio.emit('game_over', {
                        'winner': winner_name,
                        'scores': {p.name: game_manager.game.scores[i] for i, p in enumerate(game_manager.players)}
                    }, room=room_id)
                    break
                    
            else:
                # 轮到人类玩家，停止AI处理
                break
                
    except Exception as e:
        print(f"AI回合处理错误: {e}")
        socketio.emit('error', {
            'message': f'AI回合处理错误: {str(e)}'
        }, room=room_id)

if __name__ == '__main__':
    print("🎮 UNO游戏服务器启动中...")
    print("🌐 游戏将在 http://localhost:5000 运行")
    print("📱 支持实时WebSocket通信")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000) 